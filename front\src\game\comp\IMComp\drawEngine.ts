/**
 * Основной движок отрисовки изометрической карты
 */

import * as React from 'react'
import { WorldMap } from '../../../shared/types/World'
import { getTileCenterOnScreen, isoToScreen } from './coordinateUtils'
import { drawTile, drawAnimatedFogOfWar } from './renderUtils'
import { CameraRef } from './eventHandlers'
import { TILE_GAP } from './constants'
import { fogAnimationManager } from './fogAnimation'

/**
 * Основная функция отрисовки карты
 */
export const createDrawFunction = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  cameraRef: React.RefObject<CameraRef>,
  currentWorld: WorldMap | null,
  fogImagesRef: React.RefObject<HTMLImageElement[]>,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number
) => {
  return () => {
    const canvas = canvasRef.current
    if (!canvas || !cameraRef.current) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    
    ctx.imageSmoothingEnabled = false // отключаем сглаживание

    // Обновляем анимацию тумана войны
    fogAnimationManager.update(performance.now())

    // Очищаем канвас
    ctx.clearRect(0, 0, canvasWidth, canvasHeight)

    const mapSize = currentWorld?.settings?.worldSize || 20

    // Центр экрана
    const centerX = cameraRef.current.x
    const centerY = cameraRef.current.y

    // Собираем видимые тайлы для оптимизации
    const visibleTileKeys = new Set<string>()

    // Отрисовываем все тайлы
    for (let isoY = 0; isoY < mapSize; isoY++) {
      for (let isoX = 0; isoX < mapSize; isoX++) {
        const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight)

        const worldX = screenX + canvasWidth / 2 - centerX
        const worldY = screenY + canvasHeight / 2 - centerY

        // Проверяем, находится ли тайл в видимой области (грубая проверка)
        if (
          worldX >= -tileWidth &&
          worldX <= canvasWidth + tileWidth &&
          worldY >= -tileHeight &&
          worldY <= canvasHeight + tileHeight
        ) {
          const tileKey = `${isoX},${isoY}`
          visibleTileKeys.add(tileKey)

          drawTile(
            ctx,
            screenX,
            screenY,
            isoX,
            isoY,
            tileWidth,
            tileHeight,
            canvasWidth,
            canvasHeight,
            centerX,
            centerY,
            currentWorld,
            fogImagesRef
          )

          // Отрисовываем анимированный туман войны
          const tileData = currentWorld?.worldMap?.[tileKey];
          const fogImages = fogImagesRef.current;
          if (tileData?.fogOfWar && Array.isArray(fogImages) && fogImages.length) {
            // Проверяем, что все изображения загружены
            const allImagesLoaded = fogImages.every(img => img?.complete)
            if (allImagesLoaded) {
              const { centerX: tileCenterX, centerY: tileCenterY } = getTileCenterOnScreen(screenX, screenY, canvasWidth, canvasHeight, centerX, centerY);
              const halfTileW = tileWidth / 2 - TILE_GAP;
              const halfTileH = tileHeight / 2 - TILE_GAP;

              drawAnimatedFogOfWar(
                ctx,
                tileCenterX,
                tileCenterY,
                halfTileW,
                halfTileH,
                tileData,
                fogImages,
                tileWidth,
                tileHeight,
                tileKey
              );
            }
          }
        }
      }
    }

    // Очищаем состояние анимации для невидимых тайлов
    fogAnimationManager.cleanup(visibleTileKeys)

    // Рисуем центральную точку для ориентации (скрыта по умолчанию)
    ctx.fillStyle = '#ff353500'
    ctx.beginPath()
    ctx.arc(canvasWidth / 2, canvasHeight / 2, 3, 0, 2 * Math.PI)
    ctx.fill()

   
  }
}
