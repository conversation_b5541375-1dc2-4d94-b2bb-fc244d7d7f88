/**
 * Утилиты для отрисовки изометрической карты
 */

import { WorldMap, WorldMapCell } from '../../../shared/types/World';
import { TerrainType } from '../../../shared/enums';
import { TILE_GAP } from './constants';
import { getTileCenterOnScreen, isTileVisible } from './coordinateUtils';
import { fogAnimationManager } from './fogAnimation';

/**
 * Получает цвет заливки для тайла на основе его типа местности
 */
export const getTileColor = (tileData: WorldMapCell | undefined): { fillColor: string; strokeColor: string } => {
  let fillColor = '#0000001a'; // По умолчанию
  let strokeColor = '#444';

  if (!tileData) {
    return { fillColor, strokeColor };
  }

  // Цвета для разных типов местности
  switch (tileData.terrain) {
    case TerrainType.WATER:
      fillColor = '#123652ff';
      break;
    case TerrainType.GRASS:
      fillColor = '#1f5c28ff';
      break;
    case TerrainType.DEADFOREST:
      fillColor = '#6E370F';
      break;
    case TerrainType.MOUNTAIN:
      fillColor = '#646464';
      break;
    case TerrainType.DESERT:
      fillColor = '#daca7cff';
      break;
    case TerrainType.SWAMP:
      fillColor = '#2a422aff';
      break;
    case TerrainType.ROAD:
      fillColor = '#2c2b2bff';
      break;
    case TerrainType.CITY:
      fillColor = '#FFFFFF';
      break;
    case TerrainType.RUINS:
      fillColor = '#3f3730ff';
      break;
    case TerrainType.WASTELAND:
      fillColor = '#615039ff';
      break;
  }

  // Если тайл заблокирован, делаем его красноватым
  if (tileData.blocked) {
    fillColor = 'rgba(255, 0, 0, 0.2)';
    strokeColor = '#800';
  }

  return { fillColor, strokeColor };
};

/**
 * Отрисовывает ромбовидный тайл
 */
export const drawTile = (
  ctx: CanvasRenderingContext2D,
  screenX: number,
  screenY: number,
  isoX: number,
  isoY: number,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  cameraX: number,
  cameraY: number,
  currentWorld: WorldMap | null,
  fogImagesRef: React.RefObject<HTMLImageElement[]>
) => {
  const { centerX, centerY } = getTileCenterOnScreen(screenX, screenY, canvasWidth, canvasHeight, cameraX, cameraY);

  // Проверяем, находится ли тайл в видимой области
  if (!isTileVisible(centerX, centerY, tileWidth, tileHeight, canvasWidth, canvasHeight)) {
    return;
  }

  const halfTileW = tileWidth / 2 - TILE_GAP;
  const halfTileH = tileHeight / 2 - TILE_GAP;

  // Получаем данные тайла из текущего мира
  const tileKey = `${isoX},${isoY}`;
  const tileData = currentWorld?.worldMap?.[tileKey];

  // Рисуем ромб с отступом
  ctx.beginPath();
  ctx.moveTo(centerX, centerY - halfTileH); // Верх
  ctx.lineTo(centerX + halfTileW, centerY); // Право
  ctx.lineTo(centerX, centerY + halfTileH); // Низ
  ctx.lineTo(centerX - halfTileW, centerY); // Лево
  ctx.closePath();

  // Определяем цвет заливки на основе данных тайла
  const { fillColor, strokeColor } = getTileColor(tileData);

  // Проверяем, является ли это позицией игрока
  let finalFillColor = fillColor;
  if (currentWorld?.player?.position && isoX === currentWorld.player.position.x && isoY === currentWorld.player.position.y) {
    finalFillColor = '#c212b3ff'; // цвет игрока
  }

  // Если есть туман войны, отрисовываем его

  // Заливка
  ctx.fillStyle = finalFillColor;
  ctx.fill();

  // Рисуем локацию, если есть
  if (tileData?.location && !tileData.fogOfWar) {
    ctx.fillStyle = '#FFD700';
    ctx.beginPath();
    ctx.arc(centerX, centerY - 5, 3, 0, 2 * Math.PI);
    ctx.fill();
  }
};

/**
 * Отрисовывает туман войны на тайле (старая версия без анимации)
 */
export const drawFogOfWar = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  tileData: WorldMapCell,
  fogImage: HTMLImageElement,
  tileWidth: number,
  tileHeight: number
) => {
  ctx.save();
  ctx.beginPath();
  ctx.moveTo(centerX, centerY - halfTileH);
  ctx.lineTo(centerX + halfTileW, centerY);
  ctx.lineTo(centerX, centerY + halfTileH);
  ctx.lineTo(centerX - halfTileW, centerY);
  ctx.closePath();
  ctx.clip();

  // Поворот изображения в зависимости от imgDirection
  ctx.translate(centerX, centerY);
  let angle = 0;
  if (tileData.imgDirection === 2) angle = Math.PI / 2;
  if (tileData.imgDirection === 3) angle = Math.PI;
  if (tileData.imgDirection === 4) angle = (3 * Math.PI) / 2;
  ctx.rotate(angle);

  // COVER: вычисляем коэффициент для полного покрытия ромба
  const imgRatio = fogImage.width / fogImage.height;
  const tileRatio = tileWidth / tileHeight;
  let drawW = tileWidth,
    drawH = tileHeight;
  if (imgRatio > tileRatio) {
    // Изображение шире, чем ромб — увеличиваем высоту
    drawH = tileWidth / imgRatio;
    if (drawH < tileHeight) drawH = tileHeight;
    drawW = drawH * imgRatio;
  } else {
    // Изображение выше — увеличиваем ширину
    drawW = tileHeight * imgRatio;
    if (drawW < tileWidth) drawW = tileWidth;
    drawH = drawW / imgRatio;
  }

  ctx.drawImage(fogImage, -drawW / 2, -drawH / 2, drawW, drawH);

  ctx.restore();
};

/**
 * Отрисовывает анимированный туман войны на тайле
 */
export const drawAnimatedFogOfWar = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  tileData: WorldMapCell,
  fogImages: HTMLImageElement[],
  tileWidth: number,
  tileHeight: number,
  tileKey: string
) => {
  // Инициализируем анимацию для тайла если нужно
  fogAnimationManager.initializeTile(tileKey, tileData.fogVariation || 1);

  // Получаем состояние анимации
  const animationState = fogAnimationManager.getTileAnimationState(tileKey);
  if (!animationState) return;

  // Подготавливаем область отрисовки
  ctx.save();
  ctx.beginPath();
  ctx.moveTo(centerX, centerY - halfTileH);
  ctx.lineTo(centerX + halfTileW, centerY);
  ctx.lineTo(centerX, centerY + halfTileH);
  ctx.lineTo(centerX - halfTileW, centerY);
  ctx.closePath();
  ctx.clip();

  // Поворот изображения в зависимости от imgDirection
  ctx.translate(centerX, centerY);
  let angle = 0;
  if (tileData.imgDirection === 2) angle = Math.PI / 2;
  if (tileData.imgDirection === 3) angle = Math.PI;
  if (tileData.imgDirection === 4) angle = (3 * Math.PI) / 2;
  ctx.rotate(angle);

  // Проверяем кэш прозрачности
  let opacities = fogAnimationManager.getCachedOpacities(tileKey);
  if (!opacities) {
    // Вычисляем прозрачности для всех изображений
    opacities = [];
    for (let i = 0; i < fogImages.length; i++) {
      opacities[i] = fogAnimationManager.getInterpolatedOpacity(animationState, i);
    }
    fogAnimationManager.updateOpacityCache(tileKey, opacities);
  }

  // Отрисовываем только видимые изображения
  for (let i = 0; i < fogImages.length; i++) {
    const opacity = opacities[i];
    if (opacity <= 0.01) continue; // Пропускаем почти невидимые изображения

    const fogImage = fogImages[i];
    if (!fogImage?.complete) continue;

    // Устанавливаем прозрачность
    ctx.globalAlpha = opacity;

    // Вычисляем размеры для полного покрытия ромба (кэшируем результат)
    const imgRatio = fogImage.width / fogImage.height;
    const tileRatio = tileWidth / tileHeight;
    let drawW = tileWidth, drawH = tileHeight;

    if (imgRatio > tileRatio) {
      drawH = tileWidth / imgRatio;
      if (drawH < tileHeight) drawH = tileHeight;
      drawW = drawH * imgRatio;
    } else {
      drawW = tileHeight * imgRatio;
      if (drawW < tileWidth) drawW = tileWidth;
      drawH = drawW / imgRatio;
    }

    // Отрисовываем изображение
    ctx.drawImage(fogImage, -drawW / 2, -drawH / 2, drawW, drawH);
  }

  ctx.restore();
};
